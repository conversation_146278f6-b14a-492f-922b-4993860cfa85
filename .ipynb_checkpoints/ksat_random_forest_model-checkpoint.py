"""
Ksat Prediction using Random Forest with Overfitting Prevention
=============================================================

This script implements a Random Forest model to predict soil hydraulic conductivity (Ksat)
using various soil properties and environmental variables. The model includes comprehensive
preprocessing, feature selection, hyperparameter optimization, and proper validation.

Author: AI Assistant
Date: 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import pickle
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)

def load_and_explore_data(filepath='data.xlsx'):
    """Load data from Excel file and perform initial exploration."""
    print("=" * 60)
    print("STEP 1: DATA LOADING AND EXPLORATION")
    print("=" * 60)
    
    # Load data
    data = pd.read_excel(filepath)
    print(f"Dataset shape: {data.shape}")
    print(f"Number of observations: {len(data)}")
    
    # Display basic info
    print("\nDataset columns:")
    print(data.columns.tolist())
    
    print("\nFirst few rows:")
    print(data.head())
    
    print("\nTarget variable (Ksat) statistics:")
    print(data['Ksat'].describe())
    
    # Check for missing values
    print("\nMissing values:")
    missing_values = data.isnull().sum()
    if missing_values.sum() > 0:
        print(missing_values[missing_values > 0])
    else:
        print("No missing values found!")
    
    return data

def preprocess_data(data):
    """Comprehensive data preprocessing."""
    print("\n" + "=" * 60)
    print("STEP 2: DATA PREPROCESSING")
    print("=" * 60)
    
    # Create a copy to avoid modifying original data
    processed_data = data.copy()
    
    # 1. Log transformation of target variable (Ksat)
    print("1. Applying log transformation to Ksat...")
    processed_data['log_Ksat'] = np.log1p(processed_data['Ksat'])
    
    # Visualize the transformation
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    ax1.hist(processed_data['Ksat'], bins=50, alpha=0.7, color='blue')
    ax1.set_title('Original Ksat Distribution')
    ax1.set_xlabel('Ksat')
    ax1.set_ylabel('Frequency')
    
    ax2.hist(processed_data['log_Ksat'], bins=50, alpha=0.7, color='green')
    ax2.set_title('Log-transformed Ksat Distribution')
    ax2.set_xlabel('log(Ksat)')
    ax2.set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig('ksat_transformation.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. Handle categorical variables
    print("2. Processing categorical variables...")
    
    # One-hot encode 'Texture' (string categorical)
    if 'Texture' in processed_data.columns:
        texture_dummies = pd.get_dummies(processed_data['Texture'], prefix='Texture')
        processed_data = pd.concat([processed_data, texture_dummies], axis=1)
        processed_data = processed_data.drop('Texture', axis=1)
        print(f"   - Texture encoded into {len(texture_dummies.columns)} dummy variables")
    
    # Handle 'LU' (numeric categorical) - treat as categorical
    if 'LU' in processed_data.columns:
        lu_dummies = pd.get_dummies(processed_data['LU'], prefix='LU')
        processed_data = pd.concat([processed_data, lu_dummies], axis=1)
        processed_data = processed_data.drop('LU', axis=1)
        print(f"   - LU encoded into {len(lu_dummies.columns)} dummy variables")
    
    # 3. Remove original Ksat (keep log_Ksat as target)
    if 'Ksat' in processed_data.columns:
        processed_data = processed_data.drop('Ksat', axis=1)
    
    print(f"3. Final dataset shape after preprocessing: {processed_data.shape}")
    
    return processed_data

def prepare_features_target(data):
    """Separate features and target variable."""
    print("\n" + "=" * 60)
    print("STEP 3: FEATURE AND TARGET PREPARATION")
    print("=" * 60)
    
    # Separate features and target
    X = data.drop('log_Ksat', axis=1)
    y = data['log_Ksat']
    
    feature_names = X.columns.tolist()
    
    print(f"Features shape: {X.shape}")
    print(f"Target shape: {y.shape}")
    print(f"Number of features: {len(feature_names)}")
    
    return X, y, feature_names

def split_data(X, y, random_state=42):
    """Split data into train/validation/test sets (60/20/20)."""
    print("\n" + "=" * 60)
    print("STEP 4: DATA SPLITTING")
    print("=" * 60)
    
    # First split: 60% train, 40% temp
    X_train, X_temp, y_train, y_temp = train_test_split(
        X, y, test_size=0.4, random_state=random_state
    )
    
    # Second split: 20% validation, 20% test from the 40% temp
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=random_state
    )
    
    print(f"Training set: {X_train.shape[0]} samples ({X_train.shape[0]/len(X)*100:.1f}%)")
    print(f"Validation set: {X_val.shape[0]} samples ({X_val.shape[0]/len(X)*100:.1f}%)")
    print(f"Test set: {X_test.shape[0]} samples ({X_test.shape[0]/len(X)*100:.1f}%)")
    
    return X_train, X_val, X_test, y_train, y_val, y_test

def feature_selection_and_scaling(X_train, X_val, X_test, y_train, feature_names):
    """Normalize data and select top 10 features."""
    print("\n" + "=" * 60)
    print("STEP 5: FEATURE SCALING AND SELECTION")
    print("=" * 60)
    
    # 1. Scale features
    print("1. Scaling features using StandardScaler...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)
    
    # 2. Feature selection (top 10 features)
    print("2. Selecting top 10 features...")
    feature_selector = SelectKBest(score_func=f_regression, k=10)
    X_train_selected = feature_selector.fit_transform(X_train_scaled, y_train)
    X_val_selected = feature_selector.transform(X_val_scaled)
    X_test_selected = feature_selector.transform(X_test_scaled)
    
    # Get selected feature names
    selected_indices = feature_selector.get_support(indices=True)
    selected_features = [feature_names[i] for i in selected_indices]
    
    print(f"Selected features ({len(selected_features)}):")
    for i, feature in enumerate(selected_features, 1):
        print(f"   {i}. {feature}")
    
    return X_train_selected, X_val_selected, X_test_selected, selected_features, scaler, feature_selector

def hyperparameter_optimization(X_train, y_train, random_state=42):
    """Optimize Random Forest hyperparameters using GridSearchCV with 10-fold CV."""
    print("\n" + "=" * 60)
    print("STEP 6: HYPERPARAMETER OPTIMIZATION")
    print("=" * 60)
    
    # Define parameter grid for overfitting prevention
    param_grid = {
        'n_estimators': [100, 200, 300],
        'max_depth': [10, 15, 20, None],
        'min_samples_split': [5, 10, 15],
        'min_samples_leaf': [2, 4, 6],
        'max_features': ['sqrt', 'log2', 0.3]
    }
    
    print("Parameter grid:")
    for param, values in param_grid.items():
        print(f"   {param}: {values}")
    
    # Create Random Forest with overfitting prevention
    rf = RandomForestRegressor(
        random_state=random_state,
        bootstrap=True,  # Enable bootstrapping
        oob_score=True,  # Out-of-bag score for validation
        n_jobs=-1        # Use all available cores
    )
    
    # Grid search with 10-fold cross-validation
    print(f"\nPerforming grid search with 10-fold cross-validation...")
    print(f"Total combinations to test: {np.prod([len(v) for v in param_grid.values()])}")
    
    grid_search = GridSearchCV(
        estimator=rf,
        param_grid=param_grid,
        cv=10,                    # 10-fold cross-validation
        scoring='neg_mean_squared_error',
        n_jobs=-1,
        verbose=1
    )
    
    grid_search.fit(X_train, y_train)
    
    print(f"\nBest parameters found:")
    for param, value in grid_search.best_params_.items():
        print(f"   {param}: {value}")
    
    print(f"\nBest cross-validation score (RMSE): {np.sqrt(-grid_search.best_score_):.4f}")
    
    return grid_search

def evaluate_model(best_model, X_train, X_val, X_test, y_train, y_val, y_test, selected_features):
    """Comprehensive model evaluation on all data splits."""
    print("\n" + "=" * 60)
    print("STEP 7: MODEL EVALUATION")
    print("=" * 60)
    
    # Predictions on all sets
    y_train_pred = best_model.predict(X_train)
    y_val_pred = best_model.predict(X_val)
    y_test_pred = best_model.predict(X_test)
    
    # Calculate metrics for all sets
    sets = ['Training', 'Validation', 'Test']
    y_true_list = [y_train, y_val, y_test]
    y_pred_list = [y_train_pred, y_val_pred, y_test_pred]
    
    results = {}
    
    for set_name, y_true, y_pred in zip(sets, y_true_list, y_pred_list):
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)
        
        results[set_name] = {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R²': r2
        }
        
        print(f"\n{set_name} Set Metrics:")
        print(f"   MSE:  {mse:.4f}")
        print(f"   RMSE: {rmse:.4f}")
        print(f"   MAE:  {mae:.4f}")
        print(f"   R²:   {r2:.4f}")
    
    # Overfitting check
    train_r2 = results['Training']['R²']
    val_r2 = results['Validation']['R²']
    test_r2 = results['Test']['R²']
    
    print(f"\n" + "-" * 40)
    print("OVERFITTING ANALYSIS:")
    print("-" * 40)
    print(f"Training R²:   {train_r2:.4f}")
    print(f"Validation R²: {val_r2:.4f}")
    print(f"Test R²:       {test_r2:.4f}")
    
    overfitting_score = train_r2 - val_r2
    print(f"Overfitting Score (Train R² - Val R²): {overfitting_score:.4f}")
    
    if overfitting_score < 0.05:
        print("✓ Model shows good generalization (minimal overfitting)")
    elif overfitting_score < 0.1:
        print("⚠ Model shows moderate overfitting")
    else:
        print("✗ Model shows significant overfitting")
    
    # Feature importance analysis
    plot_feature_importance(best_model, selected_features)
    
    # Residual analysis
    plot_residual_analysis(y_val, y_val_pred, y_test, y_test_pred)
    
    return results

def plot_feature_importance(model, selected_features):
    """Plot feature importance from the trained model."""
    print(f"\n" + "-" * 40)
    print("FEATURE IMPORTANCE ANALYSIS:")
    print("-" * 40)
    
    # Get feature importances
    importances = model.feature_importances_
    
    # Create DataFrame for easier handling
    feature_importance_df = pd.DataFrame({
        'feature': selected_features,
        'importance': importances
    }).sort_values('importance', ascending=False)
    
    print("Top 10 Most Important Features:")
    for i, (_, row) in enumerate(feature_importance_df.iterrows(), 1):
        print(f"   {i}. {row['feature']}: {row['importance']:.4f}")
    
    # Plot feature importance
    plt.figure(figsize=(10, 6))
    sns.barplot(data=feature_importance_df, x='importance', y='feature')
    plt.title('Feature Importance in Random Forest Model')
    plt.xlabel('Importance Score')
    plt.tight_layout()
    plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_residual_analysis(y_val, y_val_pred, y_test, y_test_pred):
    """Plot residual analysis for model diagnostics."""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Validation set plots
    # Predicted vs Actual
    axes[0, 0].scatter(y_val, y_val_pred, alpha=0.6, color='blue')
    axes[0, 0].plot([y_val.min(), y_val.max()], [y_val.min(), y_val.max()], 'r--', lw=2)
    axes[0, 0].set_xlabel('Actual log(Ksat)')
    axes[0, 0].set_ylabel('Predicted log(Ksat)')
    axes[0, 0].set_title('Validation Set: Predicted vs Actual')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Residuals vs Predicted
    residuals_val = y_val - y_val_pred
    axes[0, 1].scatter(y_val_pred, residuals_val, alpha=0.6, color='blue')
    axes[0, 1].axhline(y=0, color='r', linestyle='--')
    axes[0, 1].set_xlabel('Predicted log(Ksat)')
    axes[0, 1].set_ylabel('Residuals')
    axes[0, 1].set_title('Validation Set: Residuals vs Predicted')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Test set plots
    # Predicted vs Actual
    axes[1, 0].scatter(y_test, y_test_pred, alpha=0.6, color='green')
    axes[1, 0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    axes[1, 0].set_xlabel('Actual log(Ksat)')
    axes[1, 0].set_ylabel('Predicted log(Ksat)')
    axes[1, 0].set_title('Test Set: Predicted vs Actual')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Residuals vs Predicted
    residuals_test = y_test - y_test_pred
    axes[1, 1].scatter(y_test_pred, residuals_test, alpha=0.6, color='green')
    axes[1, 1].axhline(y=0, color='r', linestyle='--')
    axes[1, 1].set_xlabel('Predicted log(Ksat)')
    axes[1, 1].set_ylabel('Residuals')
    axes[1, 1].set_title('Test Set: Residuals vs Predicted')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('residual_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def save_model_summary(results, grid_search, selected_features):
    """Save a comprehensive model summary to a text file."""
    with open('model_summary.txt', 'w') as f:
        f.write("KSAT RANDOM FOREST MODEL SUMMARY\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("BEST HYPERPARAMETERS:\n")
        f.write("-" * 25 + "\n")
        for param, value in grid_search.best_params_.items():
            f.write(f"{param}: {value}\n")
        
        f.write(f"\nBest CV Score (RMSE): {np.sqrt(-grid_search.best_score_):.4f}\n")
        
        f.write(f"\nSELECTED FEATURES:\n")
        f.write("-" * 20 + "\n")
        for i, feature in enumerate(selected_features, 1):
            f.write(f"{i}. {feature}\n")
        
        f.write(f"\nMODEL PERFORMANCE:\n")
        f.write("-" * 20 + "\n")
        for set_name, metrics in results.items():
            f.write(f"\n{set_name} Set:\n")
            for metric, value in metrics.items():
                f.write(f"   {metric}: {value:.4f}\n")
    
    print("\nModel summary saved to 'model_summary.txt'")

def main():
    """Main execution function."""
    print("KSAT PREDICTION USING RANDOM FOREST")
    print("=" * 80)
    print("This script implements a comprehensive Random Forest model")
    print("for predicting soil hydraulic conductivity (Ksat) with")
    print("overfitting prevention strategies.")
    print("=" * 80)
    
    try:
        # Step 1: Load and explore data
        data = load_and_explore_data('data.xlsx')
        
        # Step 2: Preprocess data
        processed_data = preprocess_data(data)
        
        # Step 3: Prepare features and target
        X, y, feature_names = prepare_features_target(processed_data)
        
        # Step 4: Split data
        X_train, X_val, X_test, y_train, y_val, y_test = split_data(X, y, RANDOM_STATE)
        
        # Step 5: Feature scaling and selection
        X_train_processed, X_val_processed, X_test_processed, selected_features, scaler, feature_selector = feature_selection_and_scaling(
            X_train, X_val, X_test, y_train, feature_names
        )
        
        # Step 6: Hyperparameter optimization
        grid_search = hyperparameter_optimization(X_train_processed, y_train, RANDOM_STATE)
        
        # Step 7: Model evaluation
        results = evaluate_model(
            grid_search.best_estimator_, X_train_processed, X_val_processed, X_test_processed,
            y_train, y_val, y_test, selected_features
        )
        
        # Save model summary
        save_model_summary(results, grid_search, selected_features)
        
        # Create model components dictionary
        model_components = {
            'model': grid_search.best_estimator_,
            'scaler': scaler,
            'feature_selector': feature_selector,
            'selected_features': selected_features,
            'results': results
        }
        
        # Save model components for future use
        with open('trained_model_components.pkl', 'wb') as f:
            pickle.dump(model_components, f)
        
        print("\n" + "=" * 80)
        print("MODEL TRAINING COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print("Generated files:")
        print("- ksat_transformation.png: Data transformation visualization")
        print("- feature_importance.png: Feature importance plot")
        print("- residual_analysis.png: Model diagnostic plots")
        print("- model_summary.txt: Comprehensive model summary")
        print("- trained_model_components.pkl: Saved model for predictions")
        
        # Return the trained model and preprocessing objects for future use
        return model_components
        
    except Exception as e:
        print(f"\nError occurred: {str(e)}")
        print("Please check your data file and try again.")
        return None

if __name__ == "__main__":
    model_components = main() 