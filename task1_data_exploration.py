#!/usr/bin/env python3
"""
Task 1: Data Loading and Initial Exploration
ML-Based Ksat Prediction Project

This script performs initial data loading and quality checks for the Ksat prediction project.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

def load_and_inspect_data(file_path):
    """
    Load the Excel file and perform initial inspection
    """
    print("="*60)
    print("TASK 1: DATA LOADING AND INITIAL EXPLORATION")
    print("="*60)
    
    # Load data
    print("\n1. Loading data from Excel file...")
    try:
        df = pd.read_excel(file_path)
        print(f"✓ Data loaded successfully!")
        print(f"✓ Shape: {df.shape}")
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return None
    
    return df

def verify_data_structure(df):
    """
    Verify the expected data structure
    """
    print("\n2. Verifying data structure...")
    
    # Check dimensions
    expected_rows = 5770
    expected_cols = 18  # 1 target + 17 predictors
    
    actual_rows, actual_cols = df.shape
    
    print(f"Expected: {expected_rows} rows, {expected_cols} columns")
    print(f"Actual: {actual_rows} rows, {actual_cols} columns")
    
    if actual_rows == expected_rows:
        print("✓ Row count matches expectation")
    else:
        print(f"⚠ Row count mismatch! Expected {expected_rows}, got {actual_rows}")
    
    if actual_cols == expected_cols:
        print("✓ Column count matches expectation")
    else:
        print(f"⚠ Column count mismatch! Expected {expected_cols}, got {actual_cols}")
    
    # Display column names
    print(f"\nColumn names ({len(df.columns)}):")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    return df

def generate_basic_statistics(df):
    """
    Generate comprehensive basic statistics
    """
    print("\n3. Generating basic statistics...")
    
    # Data info
    print("\nData Info:")
    print("-" * 40)
    df.info()
    
    # Check for missing values
    print("\nMissing Values:")
    print("-" * 40)
    missing_counts = df.isnull().sum()
    missing_percentages = (missing_counts / len(df)) * 100
    
    missing_df = pd.DataFrame({
        'Missing Count': missing_counts,
        'Missing %': missing_percentages
    })
    print(missing_df[missing_df['Missing Count'] > 0])
    
    if missing_df['Missing Count'].sum() == 0:
        print("✓ No missing values found!")
    
    # Descriptive statistics for numerical columns
    print("\nDescriptive Statistics (Numerical Variables):")
    print("-" * 60)
    numerical_cols = df.select_dtypes(include=[np.number]).columns
    desc_stats = df[numerical_cols].describe()
    print(desc_stats)
    
    # Data types summary
    print("\nData Types Summary:")
    print("-" * 40)
    dtype_summary = df.dtypes.value_counts()
    print(dtype_summary)
    
    return numerical_cols

def analyze_target_variable(df):
    """
    Specific analysis of the target variable (Ksat)
    """
    print("\n4. Analyzing Target Variable (Ksat)...")
    
    # Assuming Ksat is the target variable - need to identify it
    # Based on the PRD, we're looking for Ksat
    ksat_col = None
    for col in df.columns:
        if 'ksat' in col.lower() or 'k_sat' in col.lower():
            ksat_col = col
            break
    
    if ksat_col is None:
        print("⚠ Ksat column not found by name. Checking all columns...")
        print("Available columns:", list(df.columns))
        # Let's assume the first column might be Ksat or look for obvious candidates
        return None
    
    print(f"Target variable identified: {ksat_col}")
    
    ksat_data = df[ksat_col]
    
    # Basic statistics
    print(f"\nKsat Statistics:")
    print(f"Mean: {ksat_data.mean():.4f}")
    print(f"Median: {ksat_data.median():.4f}")
    print(f"Std: {ksat_data.std():.4f}")
    print(f"Min: {ksat_data.min():.4f}")
    print(f"Max: {ksat_data.max():.4f}")
    print(f"Skewness: {stats.skew(ksat_data):.4f}")
    print(f"Kurtosis: {stats.kurtosis(ksat_data):.4f}")
    
    # Check for skewness
    skewness = stats.skew(ksat_data)
    if abs(skewness) > 1:
        print(f"⚠ High skewness detected ({skewness:.4f}) - transformation may be needed")
    elif abs(skewness) > 0.5:
        print(f"⚠ Moderate skewness detected ({skewness:.4f}) - consider transformation")
    else:
        print(f"✓ Low skewness ({skewness:.4f}) - distribution is relatively normal")
    
    return ksat_col

def create_visualizations(df, numerical_cols, ksat_col):
    """
    Create comprehensive visualizations
    """
    print("\n5. Creating visualizations...")
    
    # Set up the plotting area
    fig = plt.figure(figsize=(20, 15))
    
    # 1. Ksat distribution
    plt.subplot(3, 4, 1)
    plt.hist(df[ksat_col], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title(f'{ksat_col} Distribution')
    plt.xlabel(ksat_col)
    plt.ylabel('Frequency')
    
    # 2. Ksat Q-Q plot
    plt.subplot(3, 4, 2)
    stats.probplot(df[ksat_col], dist="norm", plot=plt)
    plt.title(f'{ksat_col} Q-Q Plot')
    
    # 3. Box plot of Ksat
    plt.subplot(3, 4, 3)
    plt.boxplot(df[ksat_col])
    plt.title(f'{ksat_col} Box Plot')
    plt.ylabel(ksat_col)
    
    # 4. Log-transformed Ksat (preview)
    plt.subplot(3, 4, 4)
    log_ksat = np.log(df[ksat_col] + 1e-10)  # Add small constant to avoid log(0)
    plt.hist(log_ksat, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    plt.title(f'Log({ksat_col}) Distribution')
    plt.xlabel(f'Log({ksat_col})')
    plt.ylabel('Frequency')
    
    # 5-8. Distribution plots for other key variables
    other_vars = [col for col in numerical_cols if col != ksat_col][:4]
    for i, var in enumerate(other_vars, 5):
        plt.subplot(3, 4, i)
        plt.hist(df[var], bins=30, alpha=0.7, edgecolor='black')
        plt.title(f'{var} Distribution')
        plt.xlabel(var)
        plt.ylabel('Frequency')
    
    # 9. Correlation heatmap (preview)
    plt.subplot(3, 4, 9)
    corr_matrix = df[numerical_cols].corr()
    sns.heatmap(corr_matrix, annot=False, cmap='coolwarm', center=0, 
                square=True, cbar_kws={'shrink': 0.8})
    plt.title('Correlation Matrix (Preview)')
    
    # 10-12. Additional distribution plots
    if len(other_vars) > 4:
        additional_vars = other_vars[4:7]
        for i, var in enumerate(additional_vars, 10):
            plt.subplot(3, 4, i)
            plt.hist(df[var], bins=30, alpha=0.7, edgecolor='black')
            plt.title(f'{var} Distribution')
            plt.xlabel(var)
            plt.ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig('task1_data_exploration_plots.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ Visualizations saved as 'task1_data_exploration_plots.png'")

def document_data_types(df):
    """
    Document and categorize data types
    """
    print("\n6. Documenting data types...")
    
    # Categorize variables
    numerical_vars = []
    categorical_vars = []
    
    for col in df.columns:
        if df[col].dtype in ['int64', 'float64']:
            numerical_vars.append(col)
        else:
            categorical_vars.append(col)
    
    print(f"\nNumerical Variables ({len(numerical_vars)}):")
    for var in numerical_vars:
        print(f"  - {var}: {df[var].dtype}")
    
    print(f"\nCategorical Variables ({len(categorical_vars)}):")
    for var in categorical_vars:
        unique_count = df[var].nunique()
        print(f"  - {var}: {df[var].dtype} ({unique_count} unique values)")
        if unique_count <= 10:  # Show unique values if not too many
            print(f"    Values: {list(df[var].unique())}")
    
    return numerical_vars, categorical_vars

def main():
    """
    Main function to execute Task 1
    """
    # Load data
    df = load_and_inspect_data('data.xlsx')
    if df is None:
        return
    
    # Verify structure
    df = verify_data_structure(df)
    
    # Generate basic statistics
    numerical_cols = generate_basic_statistics(df)
    
    # Analyze target variable
    ksat_col = analyze_target_variable(df)
    
    # Document data types
    numerical_vars, categorical_vars = document_data_types(df)
    
    # Create visualizations
    if ksat_col:
        create_visualizations(df, numerical_cols, ksat_col)
    
    # Summary report
    print("\n" + "="*60)
    print("TASK 1 COMPLETION SUMMARY")
    print("="*60)
    print(f"✓ Data loaded: {df.shape[0]} rows, {df.shape[1]} columns")
    print(f"✓ Missing values: {df.isnull().sum().sum()}")
    print(f"✓ Numerical variables: {len(numerical_vars)}")
    print(f"✓ Categorical variables: {len(categorical_vars)}")
    if ksat_col:
        print(f"✓ Target variable identified: {ksat_col}")
        print(f"✓ Target skewness: {stats.skew(df[ksat_col]):.4f}")
    print("✓ Visualizations created and saved")
    print("\nTask 1 completed successfully!")

if __name__ == "__main__":
    main()
